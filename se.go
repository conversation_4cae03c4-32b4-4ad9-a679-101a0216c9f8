package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

type teacher struct {
	name string
	age  int
}

func connect() *sql.DB {
	dsn := "root:root@tcp(127.0.0.1:3306)/university"
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal(err)
	}

	return db
}
func (t *teacher) save() error {
	db := connect()
	defer db.Close()
	q := `INSERT INTO teacher (name,age) VALUES (?,?) `
	_, err := db.Exec(q, t.name, t.age)
	return err
}
func GetAll() ([]teacher, error) {
	db := connect()
	defer db.Close()

	rows, err := db.Query(`SELECT name, age FROM teacher`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var teachers []teacher
	for rows.Next() {
		var t teacher
		err := rows.Scan(&t.name, &t.age)
		if err != nil {
			return nil, err
		}
		teachers = append(teachers, t)
	}

	// Check for errors from iterating over rows
	if err = rows.Err(); err != nil {
		return nil, err
	}

	return teachers, nil
}

func main() {
	// Create a new teacher
	t1 := teacher{
		name: "أحمد محمد",
		age:  35,
	}

	// Save the teacher to database
	err := t1.save()
	if err != nil {
		log.Printf("Error saving teacher: %v", err)
	} else {
		fmt.Println("Teacher saved successfully!")
	}

	// Create another teacher
	t2 := teacher{
		name: "فاطمة علي",
		age:  28,
	}

	err = t2.save()
	if err != nil {
		log.Printf("Error saving teacher: %v", err)
	} else {
		fmt.Println("Teacher saved successfully!")
	}

	// Get all teachers from database
	teachers, err := GetAll()
	if err != nil {
		log.Printf("Error getting teachers: %v", err)
		return
	}

	// Display all teachers
	fmt.Println("\nAll Teachers:")
	fmt.Println("=============")
	for i, teacher := range teachers {
		fmt.Printf("%d. Name: %s, Age: %d\n", i+1, teacher.name, teacher.age)
	}
}
