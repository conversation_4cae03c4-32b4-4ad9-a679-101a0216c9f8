package main

import (
	"bufio"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

type teacher struct {
	name string
	age  int
}

func connect() *sql.DB {
	dsn := "root:root@tcp(127.0.0.1:3306)/university"
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal(err)
	}

	return db
}
func (t *teacher) save() error {
	db := connect()
	defer db.Close()
	q := `INSERT INTO teacher (name,age) VALUES (?,?) `
	_, err := db.Exec(q, t.name, t.age)
	return err
}
func GetAll() ([]teacher, error) {
	db := connect()
	defer db.Close()

	rows, err := db.Query(`SELECT name, age FROM teacher`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var teachers []teacher
	for rows.Next() {
		var t teacher
		err := rows.Scan(&t.name, &t.age)
		if err != nil {
			return nil, err
		}
		teachers = append(teachers, t)
	}

	// Check for errors from iterating over rows
	if err = rows.Err(); err != nil {
		return nil, err
	}

	return teachers, nil
}

func main() {
	// إنشاء مدرس جديد
	t := teacher{name: "أحمد", age: 30}

	// حفظ في قاعدة البيانات
	err := t.save()
	if err != nil {
		fmt.Println("خطأ:", err)
		return
	}
	fmt.Println("تم الحفظ بنجاح")

	// عرض جميع المدرسين
	teachers, err := GetAll()
	if err != nil {
		fmt.Println("خطأ:", err)
		return
	}

	for _, teacher := range teachers {
		fmt.Printf("الاسم: %s، العمر: %d\n", teacher.name, teacher.age)
	}
}
