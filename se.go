package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

type teacher struct {
	name string
	age  int
}

func connect() *sql.DB {
	dsn := "root:root@tcp(127.0.0.1:3306)/university"
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal(err)
	}

	return db
}
func (t *teacher) save() error {
	db := connect()
	defer db.Close()
	q := `INSERT INTO teacher (name,age) VALUES (?,?) `
	_, err := db.Exec(q, t.name, t.age)
	return err
}
func GetAll() ([]teacher, error) {
	db := connect()
	defer db.Close()

	rows, err := db.Query(`SELECT name, age FROM teacher`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var teachers []teacher
	for rows.Next() {
		var t teacher
		err := rows.Scan(&t.name, &t.age)
		if err != nil {
			return nil, err
		}
		teachers = append(teachers, t)
	}

	return teachers, nil
}

func inputTeacher() teacher {
	var name string
	var age int

	fmt.Print("اسم المدرس: ")
	fmt.Scan(&name)

	fmt.Print("العمر: ")
	fmt.Scan(&age)

	return teacher{name: name, age: age}
}

func main() {
	fmt.Println("=== نظام إدارة المدرسين ===")

	// إدخال بيانات مدرس جديد
	t := inputTeacher()

	// حفظ في قاعدة البيانات
	err := t.save()
	if err != nil {
		fmt.Println("خطأ:", err)
		return
	}
	fmt.Println("تم حفظ المدرس بنجاح!")

	// عرض جميع المدرسين
	fmt.Println("\nجميع المدرسين:")
	teachers, err := GetAll()
	if err != nil {
		fmt.Println("خطأ:", err)
		return
	}

	for _, teacher := range teachers {
		fmt.Printf("الاسم: %s، العمر: %d\n", teacher.name, teacher.age)
	}
}
